<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;

class EnsureQueueWorker extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:ensure-worker 
                            {--restart : Restart existing workers}
                            {--max-workers=1 : Maximum number of workers to run}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ensure queue worker is running, start if not';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $restart = $this->option('restart');
        $maxWorkers = (int) $this->option('max-workers');
        
        $this->info('🔍 Checking queue worker status...');

        // Check if queue workers are running
        $runningWorkers = $this->getRunningWorkers();
        $workerCount = count($runningWorkers);

        $this->info("📊 Found {$workerCount} running queue workers");

        if ($restart && $workerCount > 0) {
            $this->info('🔄 Restarting existing workers...');
            $this->restartWorkers();
            sleep(2); // Give workers time to restart
            $runningWorkers = $this->getRunningWorkers();
            $workerCount = count($runningWorkers);
        }

        if ($workerCount < $maxWorkers) {
            $workersToStart = $maxWorkers - $workerCount;
            $this->info("🚀 Starting {$workersToStart} queue worker(s)...");
            
            for ($i = 0; $i < $workersToStart; $i++) {
                $this->startWorker();
            }
            
            Log::info("Started {$workersToStart} queue workers");
        } else {
            $this->info('✅ Queue workers are already running');
        }

        // Check queue size and warn if too large
        $queueSize = \DB::table('jobs')->count();
        if ($queueSize > 1000) {
            $this->warn("⚠️  Queue has {$queueSize} jobs - consider running queue:cleanup");
            Log::warning("Large queue detected", ['queue_size' => $queueSize]);
        } else {
            $this->info("📊 Queue size: {$queueSize} jobs");
        }

        return 0;
    }

    /**
     * Get list of running queue worker processes
     */
    private function getRunningWorkers(): array
    {
        $process = new Process(['ps', 'aux']);
        $process->run();
        
        if (!$process->isSuccessful()) {
            return [];
        }
        
        $output = $process->getOutput();
        $lines = explode("\n", $output);
        
        $workers = [];
        foreach ($lines as $line) {
            if (strpos($line, 'queue:work') !== false && strpos($line, 'grep') === false) {
                $workers[] = $line;
            }
        }
        
        return $workers;
    }

    /**
     * Restart existing queue workers
     */
    private function restartWorkers(): void
    {
        // Send restart signal to workers
        $process = new Process(['php', 'artisan', 'queue:restart']);
        $process->setWorkingDirectory(base_path());
        $process->run();
        
        if (!$process->isSuccessful()) {
            $this->error('Failed to restart queue workers: ' . $process->getErrorOutput());
        }
    }

    /**
     * Start a new queue worker
     */
    private function startWorker(): void
    {
        $command = [
            'php', 
            'artisan', 
            'queue:work', 
            '--daemon',
            '--tries=3',
            '--timeout=300',
            '--sleep=3',
            '--max-jobs=1000',
            '--max-time=3600' // Restart worker every hour
        ];
        
        $process = new Process($command);
        $process->setWorkingDirectory(base_path());
        $process->setTimeout(null);
        
        // Start the process in the background
        $process->start();
        
        // Give it a moment to start
        sleep(1);
        
        if ($process->isRunning()) {
            $this->info('✅ Queue worker started successfully');
        } else {
            $this->error('❌ Failed to start queue worker: ' . $process->getErrorOutput());
        }
    }
}
