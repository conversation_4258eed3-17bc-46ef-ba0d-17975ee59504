<?php

namespace App\Services;

use App\Jobs\UpdateGeoLocationData;
use App\Mail\PasswordMail;
use App\Mail\VerificationCodeMail;
use App\Models\User;
use App\Models\UserAuthLog;
use App\Models\EmailVerificationCode;
use App\Models\UserFingerprint;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Carbon\Carbon;

class AuthService
{
    public function __construct(
        private FingerprintService $fingerprintService,
        private GeoLocationService $geoLocationService,
        private ReferralService $referralService,
        private SubscriptionService $subscriptionService,
        private OrderService $orderService,
        private PaymentService $paymentService
    ) {}

    /**
     * Register a new user
     */
    public function register(
        string $email,
        ?string $referralCode = null,
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?array $fingerprintData = null
    ): array {
        try {
            // Check if user already exists
            $existingUser = User::where('real_email', $email)->first();
            if ($existingUser) {
                return [
                    'success' => false,
                    'message' => 'Пользователь с таким email уже существует',
                    'code' => 'USER_EXISTS'
                ];
            }

            // Get geolocation data
            $geoData = $this->geoLocationService->getLocationData($ipAddress);

            // Dispatch job to update geo location data if needed
            if ($ipAddress && !$this->isLocalIp($ipAddress) && empty($geoData['country'])) {
                UpdateGeoLocationData::dispatch($ipAddress);
            }

            // Process fingerprint
            $fingerprintHash = null;
            if ($fingerprintData) {
                $fingerprintHash = $this->fingerprintService->processFingerprint(
                    null, $fingerprintData, $ipAddress, $userAgent,
                    $geoData['country'] ?? null, $geoData['city'] ?? null, $geoData
                );
            }

            // Generate verification code
            $verificationCode = EmailVerificationCode::generate(
                $email, 'registration', null, $ipAddress
            );

            // Send verification email
            $this->sendVerificationEmail($email, $verificationCode->code, 'registration');

            // Log registration attempt
            UserAuthLog::logAction(
                null, $email, 'register', 'email_code', false,
                $ipAddress, $userAgent, $fingerprintHash,
                $geoData['country'] ?? null, $geoData['city'] ?? null,
                session()->getId(), ['referral_code' => $referralCode]
            );

            return [
                'success' => true,
                'message' => 'Код подтверждения отправлен на email',
                'verification_required' => true
            ];

        } catch (\Exception $e) {
            Log::error('Registration error: ' . $e->getMessage(), [
                'email' => $email,
                'ip' => $ipAddress,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Произошла ошибка при регистрации',
                'code' => 'REGISTRATION_ERROR'
            ];
        }
    }

    /**
     * Verify registration code and complete registration
     */
    public function verifyRegistration(
        string $email,
        string $code,
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?array $fingerprintData = null
    ): array {
        try {
            // Verify code
            $verificationCode = EmailVerificationCode::verify($email, $code, 'registration');
            if (!$verificationCode) {
                UserAuthLog::logAction(
                    null, $email, 'email_verify', 'email_code', false,
                    $ipAddress, $userAgent, null, null, null,
                    session()->getId(), null, 'Invalid or expired verification code'
                );

                return [
                    'success' => false,
                    'message' => 'Неверный или истекший код подтверждения',
                    'code' => 'INVALID_CODE'
                ];
            }

            // Get geolocation data
            $geoData = $this->geoLocationService->getLocationData($ipAddress);

            // Dispatch job to update geo location data if needed
            if ($ipAddress && !$this->isLocalIp($ipAddress) && empty($geoData['country'])) {
                UpdateGeoLocationData::dispatch($ipAddress);
            }

            // Create user
            $user = $this->createUser($email, $ipAddress, $userAgent, $geoData);

            // Process fingerprint
            $fingerprintHash = null;
            if ($fingerprintData) {
                $fingerprintHash = $this->fingerprintService->processFingerprint(
                    $user->id, $fingerprintData, $ipAddress, $userAgent,
                    $geoData['country'] ?? null, $geoData['city'] ?? null, $geoData
                );
            }

            // Handle referral
            $this->handleReferral($user);

            // Activate demo subscription
            $this->activateDemoSubscription($user);

            // Generate password and send to email
            $password = $this->generateAndSendPassword($user);

            // Log successful registration
            UserAuthLog::logAction(
                $user->id, $email, 'register', 'email_code', true,
                $ipAddress, $userAgent, $fingerprintHash,
                $geoData['country'] ?? null, $geoData['city'] ?? null,
                session()->getId()
            );

            return [
                'success' => true,
                'message' => 'Регистрация завершена. Пароль отправлен на email.',
                'user' => $user,
                'password_sent' => true
            ];

        } catch (\Exception $e) {
            Log::error('Registration verification error: ' . $e->getMessage(), [
                'email' => $email,
                'code' => $code,
                'ip' => $ipAddress,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Произошла ошибка при подтверждении регистрации',
                'code' => 'VERIFICATION_ERROR'
            ];
        }
    }

    /**
     * Login user
     */
    public function login(
        string $email,
        ?string $password = null,
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?array $fingerprintData = null
    ): array {
        try {
            // Find user
            $user = User::where('real_email', $email)->first();

            // Get geolocation data
            $geoData = $this->geoLocationService->getLocationData($ipAddress);

            // Dispatch job to update geo location data if needed
            if ($ipAddress && !$this->isLocalIp($ipAddress) && empty($geoData['country'])) {
                UpdateGeoLocationData::dispatch($ipAddress);
            }

            // Process fingerprint
            $fingerprintHash = null;
            if ($fingerprintData) {
                $fingerprintHash = $this->fingerprintService->processFingerprint(
                    $user?->id, $fingerprintData, $ipAddress, $userAgent,
                    $geoData['country'] ?? null, $geoData['city'] ?? null, $geoData
                );
            }

            if (!$user) {
                // User doesn't exist, suggest registration
                UserAuthLog::logAction(
                    null, $email, 'failed_login', 'password', false,
                    $ipAddress, $userAgent, $fingerprintHash,
                    $geoData['country'] ?? null, $geoData['city'] ?? null,
                    session()->getId(), null, 'User not found'
                );

                return [
                    'success' => false,
                    'message' => 'Пользователь не найден',
                    'code' => 'USER_NOT_FOUND',
                    'suggest_registration' => true
                ];
            }

            // If password provided, try password login
            if ($password) {
                if (Hash::check($password, $user->password)) {
                    return $this->completeLogin($user, 'password', $ipAddress, $userAgent, $fingerprintHash, $geoData);
                } else {
                    UserAuthLog::logAction(
                        $user->id, $email, 'failed_login', 'password', false,
                        $ipAddress, $userAgent, $fingerprintHash,
                        $geoData['country'] ?? null, $geoData['city'] ?? null,
                        session()->getId(), null, 'Invalid password'
                    );

                    return [
                        'success' => false,
                        'message' => 'Неверный пароль',
                        'code' => 'INVALID_PASSWORD'
                    ];
                }
            }

            // No password provided, send email code
            $verificationCode = EmailVerificationCode::generate(
                $email, 'login', $user->id, $ipAddress
            );

            $this->sendVerificationEmail($email, $verificationCode->code, 'login');

            UserAuthLog::logAction(
                $user->id, $email, 'login', 'email_code', false,
                $ipAddress, $userAgent, $fingerprintHash,
                $geoData['country'] ?? null, $geoData['city'] ?? null,
                session()->getId(), ['code_sent' => true]
            );

            return [
                'success' => true,
                'message' => 'Код подтверждения отправлен на email',
                'verification_required' => true
            ];

        } catch (\Exception $e) {
            Log::error('Login error: ' . $e->getMessage(), [
                'email' => $email,
                'ip' => $ipAddress,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Произошла ошибка при входе',
                'code' => 'LOGIN_ERROR'
            ];
        }
    }

    /**
     * Verify login code
     */
    public function verifyLogin(
        string $email,
        string $code,
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?array $fingerprintData = null
    ): array {
        try {
            // Verify code
            $verificationCode = EmailVerificationCode::verify($email, $code, 'login');
            if (!$verificationCode) {
                UserAuthLog::logAction(
                    null, $email, 'failed_login', 'email_code', false,
                    $ipAddress, $userAgent, null, null, null,
                    session()->getId(), null, 'Invalid or expired verification code'
                );

                return [
                    'success' => false,
                    'message' => 'Неверный или истекший код подтверждения',
                    'code' => 'INVALID_CODE'
                ];
            }

            $user = User::where('real_email', $email)->first();
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'Пользователь не найден',
                    'code' => 'USER_NOT_FOUND'
                ];
            }

            // Get geolocation data
            $geoData = $this->geoLocationService->getLocationData($ipAddress);

            // Dispatch job to update geo location data if needed
            if ($ipAddress && !$this->isLocalIp($ipAddress) && empty($geoData['country'])) {
                UpdateGeoLocationData::dispatch($ipAddress);
            }

            // Process fingerprint
            $fingerprintHash = null;
            if ($fingerprintData) {
                $fingerprintHash = $this->fingerprintService->processFingerprint(
                    $user->id, $fingerprintData, $ipAddress, $userAgent,
                    $geoData['country'] ?? null, $geoData['city'] ?? null, $geoData
                );
            }

            return $this->completeLogin($user, 'email_code', $ipAddress, $userAgent, $fingerprintHash, $geoData);

        } catch (\Exception $e) {
            Log::error('Login verification error: ' . $e->getMessage(), [
                'email' => $email,
                'code' => $code,
                'ip' => $ipAddress,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Произошла ошибка при подтверждении входа',
                'code' => 'VERIFICATION_ERROR'
            ];
        }
    }

    /**
     * Complete login process
     */
    private function completeLogin(
        User $user,
        string $method,
        ?string $ipAddress,
        ?string $userAgent,
        ?string $fingerprintHash,
        array $geoData
    ): array {
        // Update last online
        $user->update(['last_online_at' => Carbon::now()]);

        // Create session token
        $token = $user->createToken('auth-token', ['*'], Carbon::now()->addYears(10))->plainTextToken;

        // Log successful login
        UserAuthLog::logAction(
            $user->id, $user->real_email, 'login', $method, true,
            $ipAddress, $userAgent, $fingerprintHash,
            $geoData['country'] ?? null, $geoData['city'] ?? null,
            session()->getId()
        );

        return [
            'success' => true,
            'message' => 'Вход выполнен успешно',
            'user' => $user,
            'token' => $token
        ];
    }

    /**
     * Create new user
     */
    private function createUser(string $email, ?string $ipAddress, ?string $userAgent, array $geoData): User
    {
        return User::create([
            'name' => explode('@', $email)[0],
            'real_email' => $email,
            'real_email_verified_at' => Carbon::now(),
            'email' => User::generateUniqueEmail(),
            'password' => Hash::make(Str::random(12)), // Temporary password
            'is_active' => true,
            'registered_at' => Carbon::now(),
            'currency' => 'RUB',
            'source' => 'web_registration',
            'details' => [
                'registration_ip' => $ipAddress,
                'registration_user_agent' => $userAgent,
                'registration_country' => $geoData['country'] ?? null,
                'registration_city' => $geoData['city'] ?? null,
            ]
        ]);
    }

    /**
     * Handle referral logic
     */
    private function handleReferral(User $user): void
    {
        // Используем ReferralService для получения реферального кода из запроса
        $referralCode = $this->referralService->resolveReferralFromRequest(request());

        if ($referralCode) {
            // Use new referral service
            $dto = new \App\DTOs\Referral\RegisterReferralDTO(
                invitedUserId: $user->id,
                referralCode: $referralCode->code,
                utm: [],
                details: [
                    'source' => 'web_registration',
                    'registered_at' => now()->toISOString(),
                ]
            );

            $this->referralService->registerReferral($dto);
        }
    }

    /**
     * Activate demo subscription
     */
    private function activateDemoSubscription(User $user): void
    {
        $demoPlan = SubscriptionPlan::where('code', 'demo_24h_unlimited')->first();

        if ($demoPlan) {
            $this->subscriptionService->manualCreateSubscription(
                $user,
                $demoPlan,
                null, // no order
                now(),
                now()->addHours(24),
                'Demo subscription activated on registration'
            );
        }
    }

    /**
     * Generate and send password to user
     */
    private function generateAndSendPassword(User $user): string
    {
        $password = Str::random(12);
        $user->update(['password' => Hash::make($password)]);

        // Send password email
        $this->sendPasswordEmail($user->real_email, $password);

        return $password;
    }

    /**
     * Send verification email
     */
    private function sendVerificationEmail(string $email, string $code, string $type): void
    {
        try {
            Mail::to($email)->send(new VerificationCodeMail($code, $type, $email));

            Log::info("Verification email sent successfully", [
                'email' => $email,
                'type' => $type
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to send verification email", [
                'email' => $email,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Send password email
     */
    private function sendPasswordEmail(string $email, string $password): void
    {
        try {
            Mail::to($email)->send(new PasswordMail($password, $email));

            Log::info("Password email sent successfully", [
                'email' => $email
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to send password email", [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Logout user
     */
    public function logout(User $user, ?string $ipAddress = null, ?string $userAgent = null): array
    {
        try {
            // Revoke all tokens
            $user->tokens()->delete();

            // Get geolocation data
            $geoData = $this->geoLocationService->getLocationData($ipAddress);

            // Dispatch job to update geo location data if needed
            if ($ipAddress && !$this->isLocalIp($ipAddress) && empty($geoData['country'])) {
                UpdateGeoLocationData::dispatch($ipAddress);
            }

            // Log logout
            UserAuthLog::logAction(
                $user->id, $user->real_email, 'logout', null, true,
                $ipAddress, $userAgent, null,
                $geoData['country'] ?? null, $geoData['city'] ?? null,
                session()->getId()
            );

            return [
                'success' => true,
                'message' => 'Выход выполнен успешно'
            ];

        } catch (\Exception $e) {
            Log::error('Logout error: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'ip' => $ipAddress,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Произошла ошибка при выходе',
                'code' => 'LOGOUT_ERROR'
            ];
        }
    }

    /**
     * Check if IP is local/private
     */
    private function isLocalIp(string $ip): bool
    {
        return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }
}
