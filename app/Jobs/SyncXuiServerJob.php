<?php

namespace App\Jobs;

use App\Models\XuiServer;
use App\Services\XuiServerSyncService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncXuiServerJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $timeout = 300; // 5 minutes
    public int $backoff = 60; // 1 minute between retries

    /**
     * The number of seconds after which the job's unique lock will be released.
     */
    public int $uniqueFor = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $serverId
    ) {
        //
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return "sync_xui_server_{$this->serverId}";
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting async sync for server {$this->serverId}");

        try {
            $server = XuiServer::findOrFail($this->serverId);

            if (!$server->is_active) {
                Log::info("Server {$this->serverId} is not active, skipping sync");
                return;
            }

            $syncService = new XuiServerSyncService();
            $result = $syncService->syncSingleServer($server);

            if ($result) {
                Log::info("Successfully completed async sync for server {$this->serverId}");
            } else {
                Log::warning("Async sync completed with warnings for server {$this->serverId}");
            }

        } catch (\Exception $e) {
            Log::error("Failed to sync server {$this->serverId}: {$e->getMessage()}", [
                'server_id' => $this->serverId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Sync job failed permanently for server {$this->serverId}: {$exception->getMessage()}", [
            'server_id' => $this->serverId,
            'error' => $exception->getMessage(),
        ]);
    }
}
