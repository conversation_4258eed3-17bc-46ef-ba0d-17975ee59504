<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        @if($type === 'registration')
            Подтверждение регистрации
        @elseif($type === 'login')
            Код для входа
        @else
            Код подтверждения
        @endif
    </title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #6b7280;
            font-size: 16px;
        }
        .code-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }
        .code {
            font-size: 36px;
            font-weight: bold;
            color: white;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            margin: 0;
        }
        .code-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            margin-top: 10px;
        }
        .message {
            font-size: 16px;
            line-height: 1.6;
            margin: 20px 0;
        }
        .warning {
            background: #fef3cd;
            border: 1px solid #fde68a;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            font-size: 14px;
            color: #92400e;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">SmartVPN</div>
            <div class="subtitle">Безопасный доступ к интернету</div>
        </div>

        <div class="message">
            @if($type === 'registration')
                <h2>Добро пожаловать в SmartVPN!</h2>
                <p>Спасибо за регистрацию. Для завершения создания аккаунта введите код подтверждения:</p>
            @elseif($type === 'login')
                <h2>Код для входа в систему</h2>
                <p>Вы запросили вход в систему. Введите код подтверждения для продолжения:</p>
            @else
                <h2>Код подтверждения</h2>
                <p>Введите код подтверждения для продолжения:</p>
            @endif
        </div>

        <div class="code-container">
            <div class="code">{{ $code }}</div>
            <div class="code-label">Код подтверждения</div>
        </div>

        <div class="warning">
            <strong>Важно:</strong> Код действителен в течение 15 минут. Никому не сообщайте этот код.
            Если вы не запрашивали этот код, просто проигнорируйте это письмо.
        </div>

        @if($type === 'registration')
            <div class="message">
                <p>После подтверждения регистрации:</p>
                <ul>
                    <li>Вам будет активирован демо-тариф на 24 часа</li>
                    <li>Пароль для входа будет отправлен на этот email</li>
                    <li>Вы сможете начать пользоваться VPN сразу</li>
                </ul>
            </div>
        @endif

        <div class="footer">
            <p>
                Это письмо отправлено автоматически, не отвечайте на него.<br>
                Если у вас есть вопросы, обратитесь в
                <a href="mailto:<EMAIL>">службу поддержки</a>
            </p>
            <p>
                <a href="https://smartvpn.vip">SmartVPN</a> - Ваш надежный VPN-провайдер
            </p>
        </div>
    </div>
</body>
</html>
