<?php

namespace App\Jobs;

use App\Models\Setting;
use App\Models\Subscription;
use App\Services\Notifications\NotificationManagerService;
use App\Services\SubscriptionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Illuminate\Support\Facades\Log;

class CheckSubscriptionExpirationJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $timeout = 300; // 5 minutes
    public int $backoff = 60; // 1 minute between retries

    /**
     * The number of seconds after which the job's unique lock will be released.
     */
    public int $uniqueFor = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return "check_subscription_expiration";
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting subscription expiration check job');

        try {
            $notificationService = app(NotificationManagerService::class);
            $subscriptionService = app(SubscriptionService::class);

            // Get notification settings
            $demoExpirationHours = Setting::get('notification_demo_expiration_hours', 3);
            $regularExpirationDays = Setting::get('notification_regular_expiration_days', 3);
            $regularExpirationHours = Setting::get('notification_regular_expiration_hours', 24);

            // Check for subscriptions that need expiration notifications
            $this->checkDemoSubscriptions($notificationService, $demoExpirationHours);
            $this->checkRegularSubscriptions($notificationService, $regularExpirationDays, $regularExpirationHours);

            // Check for expired subscriptions that need to be marked as expired
            $this->expireSubscriptions($subscriptionService, $notificationService);

            Log::info('Subscription expiration check job completed successfully');

        } catch (\Exception $e) {
            Log::error("Subscription expiration check job failed: {$e->getMessage()}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Check demo subscriptions for expiration warnings.
     */
    private function checkDemoSubscriptions(NotificationManagerService $notificationService, int $hoursBeforeExpiration): void
    {
        $targetTime = now()->addHours($hoursBeforeExpiration);

        $subscriptions = Subscription::active()
            ->whereHas('plan', function ($query) {
                $query->where('is_demo', true);
            })
            ->where('end_date', '<=', $targetTime)
            ->where('end_date', '>', now())
            ->with(['user', 'plan'])
            ->get();

        foreach ($subscriptions as $subscription) {
            $hoursUntilExpiration = now()->diffInHours($subscription->end_date);

            // Send notification using NotificationManagerService (it handles duplicate prevention)
            $sent = $notificationService->sendSubscriptionExpirationWarning(
                $subscription,
                $hoursUntilExpiration,
                'demo'
            );

            if ($sent) {
                Log::info("Demo expiration notification sent", [
                    'subscription_id' => $subscription->id,
                    'user_id' => $subscription->user_id,
                    'hours_until_expiration' => $hoursUntilExpiration,
                ]);
            }
        }
    }

    /**
     * Check regular subscriptions for expiration warnings.
     */
    private function checkRegularSubscriptions(NotificationManagerService $notificationService, int $daysBeforeExpiration, int $hoursBeforeExpiration): void
    {
        // Check for 3-day warning
        $targetTimeDays = now()->addDays($daysBeforeExpiration);

        $subscriptionsForDaysWarning = Subscription::active()
            ->whereHas('plan', function ($query) {
                $query->where('is_demo', false);
            })
            ->where('end_date', '<=', $targetTimeDays)
            ->where('end_date', '>', now()->addDays($daysBeforeExpiration - 1))
            ->with(['user', 'plan'])
            ->get();

        foreach ($subscriptionsForDaysWarning as $subscription) {
            $hoursUntilExpiration = now()->diffInHours($subscription->end_date);

            $sent = $notificationService->sendSubscriptionExpirationWarning(
                $subscription,
                $hoursUntilExpiration,
                'regular_days'
            );

            if ($sent) {
                Log::info("Regular expiration notification (days) sent", [
                    'subscription_id' => $subscription->id,
                    'user_id' => $subscription->user_id,
                    'hours_until_expiration' => $hoursUntilExpiration,
                ]);
            }
        }

        // Check for 24-hour warning
        $targetTimeHours = now()->addHours($hoursBeforeExpiration);

        $subscriptionsForHoursWarning = Subscription::active()
            ->whereHas('plan', function ($query) {
                $query->where('is_demo', false);
            })
            ->where('end_date', '<=', $targetTimeHours)
            ->where('end_date', '>', now())
            ->with(['user', 'plan'])
            ->get();

        foreach ($subscriptionsForHoursWarning as $subscription) {
            $hoursUntilExpiration = now()->diffInHours($subscription->end_date);

            $sent = $notificationService->sendSubscriptionExpirationWarning(
                $subscription,
                $hoursUntilExpiration,
                'regular_hours'
            );

            if ($sent) {
                Log::info("Regular expiration notification (hours) sent", [
                    'subscription_id' => $subscription->id,
                    'user_id' => $subscription->user_id,
                    'hours_until_expiration' => $hoursUntilExpiration,
                ]);
            }
        }
    }

    /**
     * Expire subscriptions that have passed their end date.
     */
    private function expireSubscriptions(SubscriptionService $subscriptionService, NotificationManagerService $notificationService): void
    {
        $expiredSubscriptions = Subscription::active()
            ->where('end_date', '<=', now())
            ->with(['user', 'plan'])
            ->get();

        foreach ($expiredSubscriptions as $subscription) {
            try {
                $subscriptionService->expireSubscription($subscription, 'Automatically expired by system');

                // Send expiration notification
                $notificationService->sendSubscriptionExpired($subscription);

                Log::info("Subscription automatically expired", [
                    'subscription_id' => $subscription->id,
                    'user_id' => $subscription->user_id,
                ]);
            } catch (\Exception $e) {
                Log::error("Failed to expire subscription {$subscription->id}: {$e->getMessage()}");
            }
        }
    }



    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Subscription expiration check job failed permanently: {$exception->getMessage()}", [
            'error' => $exception->getMessage(),
        ]);
    }
}
