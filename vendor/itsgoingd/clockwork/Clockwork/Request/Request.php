<?php namespace Clockwork\Request;

use Clockwork\Helpers\Serializer;

// Data structure representing a single application request
class Request
{
	// Unique request ID
	public $id;

	// Metadata version
	public $version = 1;

	// Request type (request, command, queue-job or test)
	public $type = 'request';

	// Request time
	public $time;

	// Request method
	public $method;

	// Request URL
	public $url;

	// Request URI
	public $uri;

	// Request headers
	public $headers = [];

	// Textual representation of the executed controller
	public $controller;

	// Request GET data
	public $getData = [];

	// Request POST data
	public $postData = [];

	// Request body data
	public $requestData = [];

	// Session data array
	public $sessionData = [];

	// Authenticated user
	public $authenticatedUser;

	// Request cookies
	public $cookies = [];

	// Response time
	public $responseTime;

	// Response processing time
	public $responseDuration;

	// Response status code
	public $responseStatus;

	// Peak memory usage in bytes
	public $memoryUsage;

	// Executed middleware
	public $middleware = [];

	// Database queries
	public $databaseQueries = [];

	// Database queries count
	public $databaseQueriesCount;

	// Database slow queries count
	public $databaseSlowQueries;

	// Database query counts of a particular type (selects, inserts, updates, deletes, others)
	public $databaseSelects;
	public $databaseInserts;
	public $databaseUpdates;
	public $databaseDeletes;
	public $databaseOthers;
	public $databaseDuration;

	// Cache queries
	public $cacheQueries = [];

	// Cache query counts of a particular type (reads, hits, writes, deletes)
	public $cacheReads;
	public $cacheHits;
	public $cacheWrites;
	public $cacheDeletes;

	// Cache queries execution time
	public $cacheTime;

	// Model actions
	public $modelsActions = [];

	// Model action counts by model
	public $modelsRetrieved = [];
	public $modelsCreated = [];
	public $modelsUpdated = [];
	public $modelsDeleted = [];

	// Redis commands
	public $redisCommands = [];

	// Dispatched queue jobs
	public $queueJobs = [];

	// Timeline events
	public $timelineData = [];

	// Log messages
	public $log = [];

	// Fired events
	public $events = [];

	// Application routes
	public $routes = [];

	// Sent notifications
	public $notifications = [];

	// Sent emails (legacy property replaced by notifications)
	public $emailsData = [];

	// Rendered views
	public $viewsData = [];

	// Custom user data
	public $userData = [];

	// HTTP requests
	public $httpRequests = [];

	// Subrequests
	public $subrequests = [];

	// Xebug profiler data
	public $xdebug = [];

	// Command name
	public $commandName;

	// Command arguments passed in
	public $commandArguments = [];

	// Command arguments defaults
	public $commandArgumentsDefaults = [];

	// Command options passed in
	public $commandOptions = [];

	// Command options defaults
	public $commandOptionsDefaults = [];

	// Command exit code
	public $commandExitCode;

	// Command output
	public $commandOutput;

	// Queue job name
	public $jobName;

	// Queue job description
	public $jobDescription;

	// Queue job status
	public $jobStatus;

	// Queue job payload
	public $jobPayload = [];

	// Queue job queue name
	public $jobQueue;

	// Queue job connection name
	public $jobConnection;

	// Queue job additional options
	public $jobOptions = [];

	// Test name
	public $testName;

	// Test status
	public $testStatus;

	// Test status message (eg. in case of failure)
	public $testStatusMessage;

	// Ran test asserts
	public $testAsserts = [];

	// Client-side performance metrics in the form of [ metric => value ]
	public $clientMetrics = [];

	// Web vitals in the form of [ vital => value ]
	public $webVitals = [];

	// Parent request
	public $parent;

	// Token to update this request data
	public $updateToken;

	// Log instance for the current request
	protected $currentLog;

	// Timeline instance for the current request
	protected $currentTimeline;

	// Create a new request, if optional data array argument is provided, it will be used to populate the request object,
	// otherwise an empty request with current time, autogenerated ID and update token will be created
	public function __construct(array $data = [])
	{
		$this->id = $data['id'] ?? $this->generateRequestId();
		$this->time = microtime(true);
		$this->updateToken = $data['updateToken'] ?? $this->generateUpdateToken();

		foreach ($data as $key => $val) $this->$key = $val;

		$this->currentLog = new Log($this->log);
		$this->currentTimeline = new Timeline\Timeline($this->timelineData);
	}

	// Get all request data as an array
	public function toArray()
	{
		return [
			'id'                       => $this->id,
			'version'                  => $this->version,
			'type'                     => $this->type,
			'time'                     => $this->time,
			'method'                   => $this->method,
			'url'                      => $this->url,
			'uri'                      => $this->uri,
			'headers'                  => $this->headers,
			'controller'               => $this->controller,
			'getData'                  => $this->getData,
			'postData'                 => $this->postData,
			'requestData'              => $this->requestData,
			'sessionData'              => $this->sessionData,
			'authenticatedUser'        => $this->authenticatedUser,
			'cookies'                  => $this->cookies,
			'responseTime'             => $this->responseTime,
			'responseStatus'           => $this->responseStatus,
			'responseDuration'         => $this->responseDuration ?? $this->getResponseDuration(),
			'memoryUsage'              => $this->memoryUsage,
			'middleware'               => $this->middleware,
			'databaseQueries'          => $this->databaseQueries,
			'databaseQueriesCount'     => $this->databaseQueriesCount ?? $this->getDatabaseQueriesCount(),
			'databaseSlowQueries'      => $this->databaseSlowQueries ?? $this->getDatabaseSlowQueriesCount(),
			'databaseSelects'          => $this->databaseSelects ?? $this->getDatabaseSelects(),
			'databaseInserts'          => $this->databaseInserts ?? $this->getDatabaseInserts(),
			'databaseUpdates'          => $this->databaseUpdates ?? $this->getDatabaseUpdates(),
			'databaseDeletes'          => $this->databaseDeletes ?? $this->getDatabaseDeletes(),
			'databaseOthers'           => $this->databaseOthers ?? $this->getDatabaseOthers(),
			'databaseDuration'         => $this->databaseDuration ?? $this->getDatabaseDuration(),
			'cacheQueries'             => $this->cacheQueries,
			'cacheReads'               => $this->cacheReads ?? $this->getCacheReads(),
			'cacheHits'                => $this->cacheHits ?? $this->getCacheHits(),
			'cacheWrites'              => $this->cacheWrites ?? $this->getCacheWrites(),
			'cacheDeletes'             => $this->cacheDeletes ?? $this->getCacheDeletes(),
			'cacheTime'                => $this->cacheTime ?? $this->getCacheTime(),
			'modelsActions'            => $this->modelsActions,
			'modelsRetrieved'          => $this->modelsRetrieved ?? $this->getModelsRetrieved(),
			'modelsCreated'            => $this->modelsCreated ?? $this->getModelsCreated(),
			'modelsUpdated'            => $this->modelsUpdated ?? $this->getModelsUpdated(),
			'modelsDeleted'            => $this->modelsDeleted ?? $this->getModelsDeleted(),
			'redisCommands'            => $this->redisCommands,
			'queueJobs'                => $this->queueJobs,
			'timelineData'             => $this->timeline()->toArray(),
			'log'                      => $this->log()->toArray(),
			'events'                   => $this->events,
			'routes'                   => $this->routes,
			'notifications'            => $this->notifications,
			'emailsData'               => $this->emailsData,
			'viewsData'                => $this->viewsData,
			'userData'                 => array_map(function ($data) {
				return $data instanceof UserData ? $data->toArray() : $data;
			}, $this->userData),
			'httpRequests'             => $this->httpRequests,
			'subrequests'              => $this->subrequests,
			'xdebug'                   => $this->xdebug,
			'commandName'              => $this->commandName,
			'commandArguments'         => $this->commandArguments,
			'commandArgumentsDefaults' => $this->commandArgumentsDefaults,
			'commandOptions'           => $this->commandOptions,
			'commandOptionsDefaults'   => $this->commandOptionsDefaults,
			'commandExitCode'          => $this->commandExitCode,
			'commandOutput'            => $this->commandOutput,
			'jobName'                  => $this->jobName,
			'jobDescription'           => $this->jobDescription,
			'jobStatus'                => $this->jobStatus,
			'jobPayload'               => $this->jobPayload,
			'jobQueue'                 => $this->jobQueue,
			'jobConnection'            => $this->jobConnection,
			'jobOptions'               => $this->jobOptions,
			'testName'                 => $this->testName,
			'testStatus'               => $this->testStatus,
			'testStatusMessage'        => $this->testStatusMessage,
			'testAsserts'              => $this->testAsserts,
			'clientMetrics'            => $this->clientMetrics,
			'webVitals'                => $this->webVitals,
			'parent'                   => $this->parent,
			'updateToken'              => $this->updateToken
		];
	}

	// Get all request data as a JSON string
	public function toJson()
	{
		return json_encode($this->toArray(), \JSON_PARTIAL_OUTPUT_ON_ERROR);
	}

	// Return request data except specified keys as an array
	public function except($keys)
	{
		return array_filter($this->toArray(), function ($value, $key) use ($keys) {
			return ! in_array($key, $keys);
		}, ARRAY_FILTER_USE_BOTH);
	}

	// Return only request data with specified keys as an array
	public function only($keys)
	{
		return array_filter($this->toArray(), function ($value, $key) use ($keys) {
			return in_array($key, $keys);
		}, ARRAY_FILTER_USE_BOTH);
	}

	// Return log instance for the current request
	public function log()
	{
		return $this->currentLog;
	}

	// Return timeline instance for the current request
	public function timeline()
	{
		return $this->currentTimeline;
	}

	// Add database query, takes query, bindings, duration (in ms) and additional data - connection (connection name),
	// time (when was the query executed), file (caller file name), line (caller line number), trace (serialized trace),
	// model (associated ORM model)
	public function addDatabaseQuery($query, $bindings = [], $duration = null, $data = [])
	{
		$this->databaseQueries[] = [
			'query'      => $query,
			'bindings'   => (new Serializer)->normalize($bindings),
			'duration'   => $duration,
			'connection' => $data['connection'] ?? null,
			'time'       => $data['time'] ?? microtime(true) - ($duration ?: 0) / 1000,
			'file'       => $data['file'] ?? null,
			'line'       => $data['line'] ?? null,
			'trace'      => $data['trace'] ?? null,
			'model'      => $data['model'] ?? null,
			'tags'       => array_merge(
				$data['tags'] ?? [], isset($data['slow']) ? [ 'slow' ] : []
			)
		];
	}

	// Add model action, takes model, action and additional data - key, attributes, changes, time (when was the action
	// executed), query, duration (in ms), connection (connection name), trace (serialized trace), file (caller file
	// name), line (caller line number), tags
	public function addModelAction($model, $action, $data = [])
	{
		$this->modelsActions[] = [
			'model'      => $model,
			'key'        => $data['key'] ?? null,
			'action'     => $action,
			'attributes' => $data['attributes'] ?? [],
			'changes'    => $data['changes'] ?? [],
			'duration'   => $duration = $data['duration'] ?? null,
			'time'       => $data['time'] ?? microtime(true) - ($duration ?: 0) / 1000,
			'query'      => $data['query'] ?? null,
			'connection' => $data['connection'] ?? null,
			'trace'      => $data['trace'] ?? null,
			'file'       => $data['file'] ?? null,
			'line'       => $data['line'] ?? null,
			'tags'       => $data['tags'] ?? []
		];
	}

	// Add cache query, takes type, key, value, duration (in ms) and additional data - connection (connection name),
	// time (when was the query executed), file (caller file name), line (caller line number), trace (serialized trace),
	// expiration
	public function addCacheQuery($type, $key, $value = null, $duration = null, $data = [])
	{
		$this->cacheQueries[] = [
			'type'       => $type,
			'key'        => $key,
			'value'      => (new Serializer)->normalize($value),
			'duration'   => $duration,
			'connection' => $data['connection'] ?? null,
			'time'       => $data['time'] ?? microtime(true) - ($duration ?: 0) / 1000,
			'file'       => $data['file'] ?? null,
			'line'       => $data['line'] ?? null,
			'trace'      => $data['trace'] ?? null,
			'expiration' => $data['expiration'] ?? null
		];
	}

	// Add event, takes event name, data, time and additional data - listeners, duration (in ms), file (caller file
	// name), line (caller line number), trace (serialized trace)
	public function addEvent($event, $eventData = null, $time = null, $data = [])
	{
		$this->events[] = [
			'event'     => $event,
			'data'      => (new Serializer)->normalize($eventData),
			'duration'  => $duration = $data['duration'] ?? null,
			'time'      => $time ?? microtime(true) - ($duration ?: 0) / 1000,
			'listeners' => $data['listeners'] ?? null,
			'file'      => $data['file'] ?? null,
			'line'      => $data['line'] ?? null,
			'trace'     => $data['trace'] ?? null
		];
	}

	// Add route, takes method, uri, action and additional data - name, middleware, before (before filters), after
	// (after filters)
	public function addRoute($method, $uri, $action, $data = [])
	{
		$this->routes[] = [
			'method'     => $method,
			'uri'        => $uri,
			'action'     => $action,
			'name'       => $data['name'] ?? null,
			'middleware' => $data['middleware'] ?? null,
			'before'     => $data['before'] ?? null,
			'after'      => $data['after'] ?? null
		];
	}

	// Add sent notifucation, takes subject, recipient, sender, and additional data - time, duration, type, content, data
	public function addNotification($subject, $to, $from = null, $data = [])
	{
		$this->notifications[] = [
			'subject'  => $subject,
			'from'     => $from,
			'to'       => $to,
			'content'  => $data['content'] ?? null,
			'type'     => $data['type'] ?? null,
			'data'     => $data['data'] ?? [],
			'duration' => $duration = $data['duration'] ?? null,
			'time'     => $data['time'] ?? microtime(true) - ($duration ?: 0) / 1000,
			'trace'    => $data['trace'] ?? null,
			'file'     => $data['file'] ?? null,
			'line'     => $data['line'] ?? null
		];
	}

	// Add sent email, takes subject, recipient address, sender address, array of headers, and additional data - time
	// (when was the email sent), duration (sending time in ms)
	public function addEmail($subject, $to, $from = null, $headers = [], $data = [])
	{
		$this->emailsData[] = [
			'start'       => $data['time'] ?? null,
			'end'         => isset($data['time'], $data['duration']) ? $data['time'] + $data['duration'] / 1000 : null,
			'duration'    => $data['duration'] ?? null,
			'description' => 'Sending an email message',
			'data'        => [
				'subject' => $subject,
				'to'      => $to,
				'from'    => $from,
				'headers' => (new Serializer)->normalize($headers)
			]
		];
	}

	// Add view, takes view name, view data and additional data - time (when was the view rendered), duration (sending
	// time in ms)
	public function addView($name, $viewData = [], $data = [])
	{
		$this->viewsData[] = [
			'start'       => $data['time'] ?? null,
			'end'         => isset($data['time'], $data['duration']) ? $data['time'] + $data['duration'] / 1000 : null,
			'duration'    => $data['duration'] ?? null,
			'description' => 'Rendering a view',
			'data'        => [
				'name' => $name,
				'data' => (new Serializer)->normalize($viewData)
			]
		];
	}

	// Add executed subrequest, takes the requested url, subrequest Clockwork ID and additional data - path if non-default
	public function addSubrequest($url, $id, $data = [])
	{
		$this->subrequests[] = [
			'url'  => $url,
			'id'   => $id,
			'path' => $data['path'] ?? null
		];
	}

	// Set the authenticated user, takes a username, an id and additional data - email and name
	public function setAuthenticatedUser($username, $id = null, $data = [])
	{
		$this->authenticatedUser = [
			'id'       => $id,
			'username' => $username,
			'email'    => $data['email'] ?? null,
			'name'     => $data['name'] ?? null
		];
	}

	// Set parent request, takes the request id and additional options - url and path if non-default
	public function setParent($id, $data = [])
	{
		$this->parent = [
			'id'   => $id,
			'url'  => $data['url'] ?? null,
			'path' => $data['path'] ?? null
		];
	}

	// Add custom user data
	public function userData($key = null)
	{
		if ($key && isset($this->userData[$key])) {
			return $this->userData[$key];
		}

		$userData = (new UserData)->title($key);

		return $key ? $this->userData[$key] = $userData : $this->userData[] = $userData;
	}

	// Add a ran test assert, takes the assert name, arguments, whether it passed and trace as arguments
	public function addTestAssert($name, $arguments = null, $passed = true, $trace = null)
	{
		$this->testAsserts[] = [
			'name'      => $name,
			'arguments' => (new Serializer)->normalize($arguments),
			'trace'     => $trace,
			'passed'    => $passed
		];
	}

	// Compute response duration in milliseconds
	public function getResponseDuration()
	{
		return ($this->responseTime - $this->time) * 1000;
	}

	// Compute total database queries count
	public function getDatabaseQueriesCount()
	{
		return count($this->databaseQueries);
	}

	// Compute total database slow queries count
	public function getDatabaseSlowQueriesCount()
	{
		return count(array_filter($this->databaseQueries, function ($query) {
			return in_array('slow', $query['tags'] ?? []);
		}));
	}

	// Compute total database select queries count
	public function getDatabaseSelects()
	{
		return count(array_filter($this->databaseQueries, function ($query) {
			return preg_match('/^select\b/i', ltrim($query['query']));
		}));
	}

	// Compute total database insert queries count
	public function getDatabaseInserts()
	{
		return count(array_filter($this->databaseQueries, function ($query) {
			return preg_match('/^insert\b/i', ltrim($query['query']));
		}));
	}

	// Compute total database update queries count
	public function getDatabaseUpdates()
	{
		return count(array_filter($this->databaseQueries, function ($query) {
			return preg_match('/^update\b/i', ltrim($query['query']));
		}));
	}

	// Compute total database delete queries count
	public function getDatabaseDeletes()
	{
		return count(array_filter($this->databaseQueries, function ($query) {
			return preg_match('/^delete\b/i', ltrim($query['query']));
		}));
	}

	// Compute total database other queries count
	public function getDatabaseOthers()
	{
		return count(array_filter($this->databaseQueries, function ($query) {
			return ! preg_match('/^(select|insert|update|delete)\b/i', ltrim($query['query']));
		}));
	}

	// Compute the sum of durations of all database queries
	public function getDatabaseDuration()
	{
		return array_reduce($this->databaseQueries, function ($total, $query) {
			return $total + ($query['duration'] ?? 0);
		}, 0);
	}

	// Compute total cache reads count
	public function getCacheReads()
	{
		return count(array_filter($this->cacheQueries, function ($query) {
			return $query['type'] == 'miss' || $query['type'] == 'hit';
		}));
	}

	// Compute total cache hits count
	public function getCacheHits()
	{
		return count(array_filter($this->cacheQueries, function ($query) {
			return $query['type'] == 'hit';
		}));
	}

	// Compute total cache writes count
	public function getCacheWrites()
	{
		return count(array_filter($this->cacheQueries, function ($query) {
			return $query['type'] == 'write';
		}));
	}

	// Compute total cache deletes count
	public function getCacheDeletes()
	{
		return count(array_filter($this->cacheQueries, function ($query) {
			return $query['type'] == 'delete';
		}));
	}

	// Compute the total time spent querying cache
	public function getCacheTime()
	{
		return array_reduce($this->cacheQueries, function ($total, $query) {
			return $total + ($query['duration'] ?? 0);
		}, 0);
	}

	// Compute total retrieved models count
	public function getModelsRetrieved()
	{
		return count(array_filter($this->modelsActions, function ($action) {
			return $action['type'] == 'retrieved';
		}));
	}

	// Compute total created models count
	public function getModelsCreated()
	{
		return count(array_filter($this->modelsActions, function ($action) {
			return $action['type'] == 'created';
		}));
	}

	// Compute total updated models count
	public function getModelsUpdated()
	{
		return count(array_filter($this->modelsActions, function ($action) {
			return $action['type'] == 'updated';
		}));
	}

	// Compute total deleted models count
	public function getModelsDeleted()
	{
		return count(array_filter($this->modelsActions, function ($action) {
			return $action['type'] == 'deleted';
		}));
	}

	// Generate unique request ID in the form of <current time>-<random number>
	protected function generateRequestId()
	{
		return str_replace('.', '-', sprintf('%.4F', microtime(true))) . '-' . mt_rand();
	}

	// Generate a random update token
	protected function generateUpdateToken()
	{
		$length = 8;
		$bytes = function_exists('random_bytes') ? random_bytes($length) : openssl_random_pseudo_bytes($length);

		return substr(bin2hex($bytes), 0, $length);
	}
}
