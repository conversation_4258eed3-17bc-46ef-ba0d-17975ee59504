<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CleanupQueueJobs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:cleanup 
                            {--dry-run : Show what would be deleted without actually deleting}
                            {--max-jobs=1000 : Maximum number of jobs to keep in queue}
                            {--older-than=1 : Delete jobs older than X hours}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old and duplicate jobs from the queue';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $maxJobs = (int) $this->option('max-jobs');
        $olderThanHours = (int) $this->option('older-than');
        
        $this->info('🧹 Starting queue cleanup...');
        
        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No jobs will be deleted');
        }

        // Get current queue statistics
        $totalJobs = DB::table('jobs')->count();
        $this->info("📊 Current queue size: {$totalJobs} jobs");

        if ($totalJobs <= $maxJobs) {
            $this->info('✅ Queue size is within limits, no cleanup needed');
            return 0;
        }

        // Clean up old jobs
        $oldJobsDeleted = $this->cleanupOldJobs($olderThanHours, $dryRun);
        
        // Clean up duplicate sync jobs
        $duplicatesDeleted = $this->cleanupDuplicateSyncJobs($dryRun);
        
        // If still too many jobs, clean up oldest jobs
        $totalJobs = DB::table('jobs')->count();
        $oldestDeleted = 0;
        
        if ($totalJobs > $maxJobs) {
            $oldestDeleted = $this->cleanupOldestJobs($maxJobs, $dryRun);
        }

        $totalDeleted = $oldJobsDeleted + $duplicatesDeleted + $oldestDeleted;
        
        if ($dryRun) {
            $this->info("🔍 Would delete {$totalDeleted} jobs:");
            $this->line("  - Old jobs (>{$olderThanHours}h): {$oldJobsDeleted}");
            $this->line("  - Duplicate sync jobs: {$duplicatesDeleted}");
            $this->line("  - Oldest jobs: {$oldestDeleted}");
        } else {
            $this->info("✅ Deleted {$totalDeleted} jobs:");
            $this->line("  - Old jobs (>{$olderThanHours}h): {$oldJobsDeleted}");
            $this->line("  - Duplicate sync jobs: {$duplicatesDeleted}");
            $this->line("  - Oldest jobs: {$oldestDeleted}");
            
            Log::info("Queue cleanup completed", [
                'total_deleted' => $totalDeleted,
                'old_jobs_deleted' => $oldJobsDeleted,
                'duplicates_deleted' => $duplicatesDeleted,
                'oldest_deleted' => $oldestDeleted,
            ]);
        }

        $finalCount = DB::table('jobs')->count();
        $this->info("📊 Final queue size: {$finalCount} jobs");

        return 0;
    }

    /**
     * Clean up jobs older than specified hours
     */
    private function cleanupOldJobs(int $olderThanHours, bool $dryRun): int
    {
        $cutoffTime = now()->subHours($olderThanHours)->timestamp;
        
        $query = DB::table('jobs')->where('created_at', '<', $cutoffTime);
        
        if ($dryRun) {
            return $query->count();
        }
        
        return $query->delete();
    }

    /**
     * Clean up duplicate sync jobs, keeping only the newest one for each server
     */
    private function cleanupDuplicateSyncJobs(bool $dryRun): int
    {
        $deleted = 0;
        
        // Find duplicate SyncXuiServerJob jobs
        $syncJobs = DB::table('jobs')
            ->whereRaw('payload LIKE ?', ['%SyncXuiServerJob%'])
            ->orderBy('created_at', 'desc')
            ->get(['id', 'payload', 'created_at']);

        $serverJobs = [];
        $jobsToDelete = [];

        foreach ($syncJobs as $job) {
            $payload = json_decode($job->payload, true);
            if (isset($payload['data']['serverId'])) {
                $serverId = $payload['data']['serverId'];
                
                if (isset($serverJobs[$serverId])) {
                    // This is a duplicate, mark for deletion
                    $jobsToDelete[] = $job->id;
                } else {
                    // This is the first (newest) job for this server
                    $serverJobs[$serverId] = $job->id;
                }
            }
        }

        if (!empty($jobsToDelete)) {
            if ($dryRun) {
                $deleted = count($jobsToDelete);
            } else {
                $deleted = DB::table('jobs')->whereIn('id', $jobsToDelete)->delete();
            }
        }

        // Do the same for SyncUserOnlineStatusJob
        $userStatusJobs = DB::table('jobs')
            ->whereRaw('payload LIKE ?', ['%SyncUserOnlineStatusJob%'])
            ->orderBy('created_at', 'desc')
            ->get(['id', 'payload', 'created_at']);

        $serverUserJobs = [];
        $userJobsToDelete = [];

        foreach ($userStatusJobs as $job) {
            $payload = json_decode($job->payload, true);
            if (isset($payload['data']['serverId'])) {
                $serverId = $payload['data']['serverId'];
                
                if (isset($serverUserJobs[$serverId])) {
                    $userJobsToDelete[] = $job->id;
                } else {
                    $serverUserJobs[$serverId] = $job->id;
                }
            }
        }

        if (!empty($userJobsToDelete)) {
            if ($dryRun) {
                $deleted += count($userJobsToDelete);
            } else {
                $deleted += DB::table('jobs')->whereIn('id', $userJobsToDelete)->delete();
            }
        }

        return $deleted;
    }

    /**
     * Clean up oldest jobs to keep queue under max size
     */
    private function cleanupOldestJobs(int $maxJobs, bool $dryRun): int
    {
        $currentCount = DB::table('jobs')->count();
        
        if ($currentCount <= $maxJobs) {
            return 0;
        }
        
        $toDelete = $currentCount - $maxJobs;
        
        $oldestJobs = DB::table('jobs')
            ->orderBy('created_at', 'asc')
            ->limit($toDelete)
            ->pluck('id');

        if ($dryRun) {
            return $oldestJobs->count();
        }
        
        return DB::table('jobs')->whereIn('id', $oldestJobs)->delete();
    }
}
