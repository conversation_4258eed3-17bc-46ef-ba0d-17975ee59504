<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use App\Jobs\DetectOfflineUsersJob;
use App\Jobs\SyncUserOnlineStatusJob;
use App\Jobs\CheckSubscriptionExpirationJob;
use App\Jobs\CheckSubscriptionTrafficJob;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Ensure queue worker is running
Schedule::command('queue:ensure-worker --max-workers=1')
    ->everyMinute()
    ->withoutOverlapping()
    ->runInBackground()
    ->description('Ensure queue worker is running');

// Clean up queue periodically
Schedule::command('queue:cleanup --max-jobs=1000 --older-than=2')
    ->everyFiveMinutes()
    ->withoutOverlapping()
    ->runInBackground()
    ->description('Clean up old and duplicate queue jobs');

// Schedule XUI server synchronization tasks (data only, no user status)
Schedule::command('xui:sync-servers --async --batch-size=5 --ignore-confirmation')
    ->everyThirtySeconds()
    ->withoutOverlapping()
    ->runInBackground()
    ->description('Sync XUI servers data asynchronously');

// Schedule user online status synchronization (more frequent)
// Schedule::command('xui:sync-user-status --async --batch-size=10 --ignore-confirmation')
//     ->everyTenSeconds()
//     ->withoutOverlapping()
//     ->runInBackground()
//     ->description('Sync user online status from XUI servers');

// Schedule offline user detection (background cleanup)
// Schedule::job(new DetectOfflineUsersJob)
//     ->everyMinute()
//     ->withoutOverlapping()
//     ->description('Detect and log offline users');

// Optional: Schedule a full sync during low traffic hours
// Schedule::command('xui:sync-servers --force --batch-size=10 --ignore-confirmation')
//     ->dailyAt('03:00')
//     ->withoutOverlapping()
//     ->description('Full XUI server sync during maintenance window');

// Schedule subscription monitoring tasks
Schedule::job(new CheckSubscriptionExpirationJob)
    ->everyMinute()
    ->withoutOverlapping()
    ->description('Check subscription expiration and send notifications');

// Schedule::job(new CheckSubscriptionTrafficJob)
//     ->everyMinute()
//     ->withoutOverlapping()
//     ->description('Check subscription traffic limits and send notifications');

// Schedule SSH keys cleanup (daily)
Schedule::command('ssh-keys:cleanup')
    ->daily()
    ->description('Cleanup old temporary SSH keys');
