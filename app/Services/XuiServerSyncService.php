<?php

namespace App\Services;

use App\Models\XuiServer;
use App\Jobs\SyncXuiServerJob;
use App\Services\Traffic\TrafficLoggerService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class XuiServerSyncService
{
    private TrafficLoggerService $trafficLoggerService;
    private int $defaultBatchSize;

    public function __construct()
    {
        $this->trafficLoggerService = new TrafficLoggerService();
        $this->defaultBatchSize = config('xui_sync.default_batch_size', 10);
    }

    /**
     * Synchronize servers with options for async processing and filtering.
     */
    public function syncServers(array $options = []): array
    {
        $async = $options['async'] ?? false;
        $batchSize = $options['batch_size'] ?? $this->defaultBatchSize;
        $serverIds = $options['server_ids'] ?? null;
        $force = $options['force'] ?? false;

        Log::info('Starting XUI server synchronization', [
            'async' => $async,
            'batch_size' => $batchSize,
            'server_ids' => $serverIds,
            'force' => $force,
        ]);

        // Get servers to sync
        $servers = $this->getServersToSync($serverIds, $force);

        if ($servers->isEmpty()) {
            Log::info('No servers found for synchronization');
            return ['synced' => 0, 'failed' => 0, 'skipped' => 0];
        }

        Log::info("Found {$servers->count()} servers for synchronization");

        if ($async) {
            return $this->syncServersAsync($servers, $batchSize);
        } else {
            return $this->syncServersSync($servers, $batchSize);
        }
    }

    /**
     * Get servers that should be synchronized.
     */
    private function getServersToSync(?array $serverIds, bool $force): Collection
    {
        $query = XuiServer::where('is_active', true);

        if ($serverIds) {
            $query->whereIn('id', $serverIds);
        }

        if (!$force) {
            $query->where('auto_sync', true);
        }

        return $query->get();
    }

    /**
     * Synchronize servers asynchronously using jobs.
     */
    private function syncServersAsync(Collection $servers, int $batchSize): array
    {
        // First, check if there are too many jobs in the queue
        $queueSize = \DB::table('jobs')->count();
        if ($queueSize > 1000) {
            Log::warning("Queue has {$queueSize} jobs, skipping new sync jobs to prevent overload");
            return [
                'synced' => 0,
                'failed' => 0,
                'skipped' => $servers->count(),
                'jobs_dispatched' => 0,
                'queue_overloaded' => true,
            ];
        }

        $batches = $servers->chunk($batchSize);
        $totalJobs = 0;
        $skippedJobs = 0;

        foreach ($batches as $batch) {
            foreach ($batch as $server) {
                // Check if a sync job for this server is already queued
                $existingJob = \DB::table('jobs')
                    ->whereRaw('payload LIKE ?', ["%sync_xui_server_{$server->id}%"])
                    ->exists();

                if (!$existingJob) {
                    SyncXuiServerJob::dispatch($server->id);
                    $totalJobs++;
                } else {
                    $skippedJobs++;
                }
            }
        }

        Log::info("Dispatched {$totalJobs} sync jobs for servers, skipped {$skippedJobs} (already queued)");

        return [
            'synced' => 0,
            'failed' => 0,
            'skipped' => $skippedJobs,
            'jobs_dispatched' => $totalJobs,
        ];
    }

    /**
     * Synchronize servers synchronously.
     */
    private function syncServersSync(Collection $servers, int $batchSize): array
    {
        $results = [
            'synced' => 0,
            'failed' => 0,
            'skipped' => 0,
            'logged' => 0,
        ];

        $batches = $servers->chunk($batchSize);

        // Process batches
        foreach ($batches as $batchIndex => $batch) {
            Log::info("Processing batch " . ($batchIndex + 1) . " with {$batch->count()} servers");

            // Process servers in batch
            foreach ($batch as $server) {
                try {
                    // Sync server and log traffic
                    $result = $this->syncSingleServer($server);

                    // Update results
                    if ($result['synced']) {
                        $results['synced']++;
                        if ($result['traffic_logged']) {
                            $results['logged']++;
                        }
                    } else {
                        $results['skipped']++;
                    }

                } catch (\Exception $e) {
                    // Update results
                    $results['failed']++;
                    Log::error("Failed to sync server {$server->id}: {$e->getMessage()}");
                }
            }
        }

        return $results;
    }

    /**
     * Synchronize a single server.
     */
    public function syncSingleServer(XuiServer $server): array
    {
        Log::info("Syncing server: {$server->name} (ID: {$server->id})");

        // Prepare variables
        $previousUpdatedAt = $server->raw_inbounds_list_updated_at;
        $trafficLogged = false;

        try {
            DB::transaction(function () use ($server) {
                $now = now();

                // 1. Sync raw data fields
                $this->syncRawServerData($server, $now);

                // 2. Update computed fields
                $this->updateComputedFields($server);

                // 3. Update last sync timestamp
                $server->touchLastSync($now);
            });

            $server->refresh();

            // ✅ Логируем трафик, если inbound'ы действительно обновились
            $updatedAt = $server->raw_inbounds_list_updated_at;
            // Check if inbounds were updated
            if ($updatedAt && (!$previousUpdatedAt || $updatedAt->gt($previousUpdatedAt))) {
                Log::info("📦 Traffic log triggered for server {$server->id} after inbound update");
                // Log traffic
                $this->trafficLoggerService->logFromXuiServer($server);
                $trafficLogged = true;
            }

            Log::info("Successfully synced server {$server->id}: {$server->name}");
            return ['synced' => true, 'traffic_logged' => $trafficLogged];

        } catch (\Exception $e) {
            Log::error("Error syncing server {$server->id} ({$server->name}): {$e->getMessage()}", [
                'server_id' => $server->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return ['synced' => false, 'traffic_logged' => false];
        }
    }

    /**
     * Sync raw data fields from server.
     */
    private function syncRawServerData(XuiServer $server, Carbon $now): void
    {
        $serverInfoService = new ServerInfoService($server);

        // Server status
        try {
            $serverStatus = $serverInfoService->getStatus();
            $server->update([
                'raw_server_status' => $serverStatus,
                'raw_server_status_updated_at' => $now,
            ]);
        } catch (\Exception $e) {
            Log::warning("Failed to get server status for server {$server->id}: {$e->getMessage()}");
        }

        // Settings
        try {
            $settings = $serverInfoService->getAllSettings();
            $server->update([
                'raw_settings_all' => $settings,
                'raw_settings_all_updated_at' => $now,
            ]);
        } catch (\Exception $e) {
            Log::warning("Failed to get settings for server {$server->id}: {$e->getMessage()}");
        }

        // Inbounds list
        try {
            $inbounds = $serverInfoService->getInbounds();
            $server->update([
                'raw_inbounds_list' => $inbounds,
                'raw_inbounds_list_updated_at' => $now,
            ]);
        } catch (\Exception $e) {
            Log::warning("Failed to get inbounds for server {$server->id}: {$e->getMessage()}");
        }

    }

    /**
     * Update computed fields based on raw data.
     */
    private function updateComputedFields(XuiServer $server): void
    {
        $updates = [];

        // Calculate server load from raw_server_status
        if ($server->raw_server_status) {
            $serverStatus = is_string($server->raw_server_status)
                ? json_decode($server->raw_server_status, true)
                : $server->raw_server_status;

            if (isset($serverStatus['cpu']) && isset($serverStatus['mem'])) {
                // calculate server load by ServerInfoService::calculateServerLoadPercentage
                $updates['server_load'] = ServerInfoService::calculateServerLoadPercentage($serverStatus);
            }
        }

        // Calculate clients count from raw_inbounds_list
        if ($server->raw_inbounds_list) {
            $inbounds = is_string($server->raw_inbounds_list)
                ? json_decode($server->raw_inbounds_list, true)
                : $server->raw_inbounds_list;

            $totalClients = 0;
            if (is_array($inbounds)) {
                foreach ($inbounds as $inbound) {
                    $totalClients += count($inbound['clientStats'] ?? []);
                }
            }
            $updates['clients_count'] = $totalClients;
        }

        if (!empty($updates)) {
            $server->update($updates);
        }
    }


}
